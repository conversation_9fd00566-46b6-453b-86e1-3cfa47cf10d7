<div class="demo-container">
  <h1 class="demo-title">Toast Notifications</h1>
  
  <div class="controls">
    <!-- Toast Types -->
    <div class="control-group">
      <h3>Toast Types</h3>
      <div class="control-grid">
        <div *ngFor="let example of toastExamples" class="example-card">
          <div class="example-content">
            <h4>{{ example.title }}</h4>
            <p>{{ example.description }}</p>
          </div>
          <ava-button 
            [label]="example.buttonLabel"
            [variant]="example.buttonVariant"
            size="medium"
            (userClick)="example.action()">
          </ava-button>
        </div>
      </div>
    </div>

    <!-- Configuration -->
    <div class="control-group">
      <h3>Configuration</h3>
      <div class="config-row">
        <div class="config-item">
          <label for="position">Position</label>
          <select 
            id="position" 
            [(ngModel)]="selectedPosition" 
            (change)="onPositionChange()">
            <option *ngFor="let pos of positions" [value]="pos.value">
              {{ pos.label }}
            </option>
          </select>
        </div>
        
        <div class="config-item">
          <label for="theme">Theme</label>
          <select 
            id="theme" 
            [(ngModel)]="selectedTheme" 
            (change)="onThemeChange()">
            <option *ngFor="let theme of themes" [value]="theme.value">
              {{ theme.label }}
            </option>
          </select>
        </div>
        
        <div class="config-item">
          <label for="duration">Duration (ms)</label>
          <input 
            type="number" 
            id="duration" 
            [(ngModel)]="duration" 
            min="1000" 
            max="10000" 
            step="500">
        </div>
      </div>
    </div>

    <!-- Custom Toast -->
    <div class="control-group">
      <h3>Custom Toast</h3>
      <div class="custom-inputs">
        <input 
          type="text" 
          placeholder="Enter title..." 
          [(ngModel)]="customTitle">
        <input 
          type="text" 
          placeholder="Enter message..." 
          [(ngModel)]="customMessage">
        <div class="custom-actions">
          <ava-button 
            label="Show Custom"
            variant="primary"
            size="medium"
            (userClick)="showCustomToast()">
          </ava-button>
          <ava-button 
            label="Clear All"
            variant="secondary"
            size="medium"
            (userClick)="clearAllToasts()">
          </ava-button>
        </div>
      </div>
    </div>

    <!-- Flexible Toast Examples -->
    <div class="control-group">
      <h3>Flexible Custom Toasts</h3>
      <div class="toast-examples">
        <ava-button
          label="Multi-Action Toast"
          variant="success"
          size="medium"
          (userClick)="showFlexibleToast()">
        </ava-button>

        <ava-button
          label="Custom Icon Toast"
          variant="warning"
          size="medium"
          (userClick)="showCustomIconToast()">
        </ava-button>

        <ava-button
          label="HTML Content Toast"
          variant="primary"
          size="medium"
          (userClick)="showHtmlToast()">
        </ava-button>

        <ava-button
          label="Default White Toast"
          variant="secondary"
          size="medium"
          (userClick)="showDefaultToast()">
        </ava-button>
      </div>
    </div>
  </div>

</div>
