/* Toast Container */
.ava-toast-container {
    position: var(--toast-container-position, fixed);
    z-index: var(--toast-container-z-index, 9999);
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: var(--toast-container-gap, var(--global-spacing-3));
    max-width: var(--toast-max-width, 400px);
    width: 100%;
}

.ava-toast-container.position-top-left {
    top: var(--toast-container-top, var(--global-spacing-4));
    left: var(--toast-container-right, var(--global-spacing-4));
}

.ava-toast-container.position-top-center {
    top: var(--toast-container-top, var(--global-spacing-4));
    left: 50%;
    transform: translateX(-50%);
}

.ava-toast-container.position-top-right {
    top: var(--toast-container-top, var(--global-spacing-4));
    right: var(--toast-container-right, var(--global-spacing-4));
}

.ava-toast-container.position-bottom-left {
    bottom: var(--toast-container-top, var(--global-spacing-4));
    left: var(--toast-container-right, var(--global-spacing-4));
    flex-direction: column-reverse;
}

.ava-toast-container.position-bottom-center {
    bottom: var(--toast-container-top, var(--global-spacing-4));
    left: 50%;
    transform: translateX(-50%);
    flex-direction: column-reverse;
}

.ava-toast-container.position-bottom-right {
    bottom: var(--toast-container-top, var(--global-spacing-4));
    right: var(--toast-container-right, var(--global-spacing-4));
    flex-direction: column-reverse;
}

/* Responsive */
@media (max-width: 768px) {
    .ava-toast-container {
        max-width: var(--toast-mobile-max-width);
        left: var(--toast-mobile-left) !important;
        right: var(--toast-mobile-right) !important;
        transform: var(--toast-mobile-transform) !important;
    }
}
