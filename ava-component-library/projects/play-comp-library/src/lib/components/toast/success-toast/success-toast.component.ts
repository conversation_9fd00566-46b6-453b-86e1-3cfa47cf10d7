import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
    Output,
    EventEmitter,
    OnInit,
    OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
import { ToastResult, ToastTheme } from '../toast-service';

@Component({
    selector: 'ava-success-toast',
    imports: [CommonModule, IconComponent, ButtonComponent],
    templateUrl: './success-toast.component.html',
    styleUrl: './success-toast.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class SuccessToastComponent implements OnInit, OnDestroy {
    @Input() title = 'Success';
    @Input() message = 'Operation completed successfully!';
    @Input() icon = 'circle-check';
    @Input() iconColor = 'var(--color-text-success)';
    @Input() iconSize = 24;
    @Input() theme: ToastTheme = 'light';
    @Input() duration = 4000;
    @Input() closable = true;
    @Input() showProgress = true;

    @Output() closed = new EventEmitter<ToastResult>();

    isVisible = false;
    isExiting = false;
    progressWidth = 0;
    private progressTimer?: number;

    constructor(private cdr: ChangeDetectorRef) {}

    ngOnInit() {
        // Trigger entrance animation
        setTimeout(() => {
            this.isVisible = true;
            this.cdr.detectChanges();
        }, 10);

        // Start progress bar animation if enabled
        if (this.showProgress && this.duration > 0) {
            this.startProgressAnimation();
        }
    }

    ngOnDestroy() {
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
        }
    }

    private startProgressAnimation() {
        const interval = 50; // Update every 50ms
        const increment = (interval / this.duration) * 100;

        this.progressTimer = window.setInterval(() => {
            this.progressWidth += increment;
            if (this.progressWidth >= 100) {
                this.progressWidth = 100;
                if (this.progressTimer) {
                    clearInterval(this.progressTimer);
                }
            }
            this.cdr.detectChanges();
        }, interval);
    }

    startExitAnimation() {
        this.isExiting = true;
        this.cdr.detectChanges();
    }

    onClose() {
        console.log('Toast close button clicked');
        this.closed.emit({ action: 'close' });
    }

    onToastClick() {
        // Optional: Handle toast click
    }
}
