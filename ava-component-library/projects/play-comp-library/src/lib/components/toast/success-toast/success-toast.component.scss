.ava-toast {
    pointer-events: var(--toast-pointer-events);
    display: var(--toast-display);
    align-items: var(--toast-align-items);
    gap: var(--toast-gap);
    padding: var(--toast-padding);
    border-radius: var(--toast-border-radius);
    box-shadow: var(--toast-shadow);
    backdrop-filter: blur(10px);
    border: 1px solid var(--toast-border);
    transition: var(--toast-transition);
    transform: translateY(100px);
    opacity: 0;
    max-width: var(--toast-max-width);
    min-width: var(--toast-min-width);
    position: var(--toast-position);
    overflow: var(--toast-overflow);
    cursor: var(--toast-cursor);
    margin-bottom: var(--toast-margin-bottom);

    &.show {
        transform: translateY(0);
        opacity: 1;
    }

    &.hide {
        transform: translateY(-100px);
        opacity: 0;
        margin-top: -80px;
    }

    /* Success Toast Specific Styling */
    &.success {
        background: var(--toast-success-background);
        color: var(--toast-success-text);
        border-color: var(--toast-success-border);
    }

    /* Theme Variations */
    &.theme-light {
        background: var(--toast-background);
        color: var(--toast-title-color);
        border-color: var(--toast-border);
    }

    &.theme-dark {
        background: var(--toast-background-dark);
        color: var(--toast-title-color-dark);
        border-color: var(--toast-border);
    }
}

/* Toast Icon */
.toast-icon {
    width: var(--toast-icon-width);
    height: var(--toast-icon-height);
    flex-shrink: var(--toast-icon-flex-shrink);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Toast Content */
.toast-content {
    flex: var(--toast-content-flex);
}

.toast-title {
    font-weight: var(--toast-title-weight);
    margin-bottom: var(--toast-title-margin-bottom);
    line-height: var(--toast-title-line-height);
    font-size: var(--toast-title-font);
    color: var(--toast-title-color);
}

.toast-message {
    font-weight: var(--toast-message-weight);
    font-size: var(--toast-message-font);
    line-height: var(--toast-message-line-height);
    color: var(--toast-message-color);
    margin-bottom: var(--toast-message-margin-bottom);
}

/* Close Button */
.toast-close {
    flex-shrink: 0;
}

/* Progress Bar */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
    transition: width linear;
}

/* Toast Actions */
.toast-actions {
    display: flex;
    gap: var(--global-spacing-2);
    margin-top: var(--global-spacing-2);
}
