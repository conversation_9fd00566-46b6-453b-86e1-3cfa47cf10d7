import {
    ApplicationRef,
    ComponentRef,
    EmbeddedViewRef,
    Injectable,
    Injector,
    Type,
    createComponent,
    EnvironmentInjector
} from '@angular/core';
import { ToastContainerComponent } from './toast-container/toast-container.component';
import { SuccessToastComponent } from './success-toast/success-toast.component';
import { ErrorToastComponent } from './error-toast/error-toast.component';
import { WarningToastComponent } from './warning-toast/warning-toast.component';
import { InfoToastComponent } from './info-toast/info-toast.component';
import { LoadingToastComponent } from './loading-toast/loading-toast.component';
import { CustomToastComponent } from './custom-toast/custom-toast.component';

// Toast Types and Interfaces
export interface ToastConfig {
    title?: string;
    message?: string;
    icon?: string;
    iconColor?: string;
    iconSize?: number;
    duration?: number;
    position?: ToastPosition;
    theme?: ToastTheme;
    closable?: boolean;
    showProgress?: boolean;
    data?: any;
}

export interface ErrorToastConfig extends ToastConfig {
    showRetryButton?: boolean;
    retryButtonText?: string;
}

export interface WarningToastConfig extends ToastConfig {
    showActionButton?: boolean;
    actionButtonText?: string;
}

export interface InfoToastConfig extends ToastConfig {
    showLearnMoreButton?: boolean;
    learnMoreButtonText?: string;
}

export interface LoadingToastConfig extends ToastConfig {
    progress?: number;
    showProgress?: boolean;
    indeterminate?: boolean;
    showCancelButton?: boolean;
    cancelButtonText?: string;
}

export interface ToastAction {
    label: string;
    action?: string;
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    disabled?: boolean;
    icon?: string;
    iconPosition?: 'left' | 'right' | 'only';
}

export interface CustomToastConfig extends ToastConfig {
    type?: ToastType;
    actions?: ToastAction[];
    size?: 'small' | 'medium' | 'large';
    showIcon?: boolean;
    customIcon?: string;
    customIconColor?: string;
    customContent?: string;
    allowHtml?: boolean;
}

export interface ToastResult {
    action?: string;
    data?: any;
    dismissed?: boolean;
}

export type ToastType = 'default' | 'success' | 'error' | 'warning' | 'info' | 'loading';
export type ToastPosition = 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';
export type ToastTheme = 'light' | 'dark';

@Injectable({ providedIn: 'root' })
export class ToastService {
    private containerRef!: ComponentRef<ToastContainerComponent>;
    private toasts = new Map<number, ComponentRef<any>>();
    private toastId = 0;
    private defaultPosition: ToastPosition = 'top-right';
    private defaultTheme: ToastTheme = 'light';

    constructor(
        private appRef: ApplicationRef,
        private injector: Injector,
        private envInjector: EnvironmentInjector
    ) {
        this.initializeContainer();
    }

    private initializeContainer() {
        // Create container if it doesn't exist
        if (!this.containerRef) {
            console.log('Initializing toast container');
            this.containerRef = createComponent(ToastContainerComponent, {
                environmentInjector: this.envInjector,
                elementInjector: this.injector
            });

            this.appRef.attachView(this.containerRef.hostView);
            const containerElem = (this.containerRef.hostView as EmbeddedViewRef<any>)
                .rootNodes[0] as HTMLElement;
            document.body.appendChild(containerElem);
            console.log('Toast container initialized and added to DOM');

            // Set default position
            this.containerRef.instance.setPosition(this.defaultPosition);
        }
    }

    show<T extends object>(component: Type<T>, config?: Partial<T>): Promise<ToastResult> {
        const id = ++this.toastId;
        console.log('Creating toast component:', component.name, 'with config:', config);

        // Create the toast component immediately
        const toastRef = this.containerRef.instance.container.createComponent(component, {
            injector: this.injector,
            environmentInjector: this.envInjector
        });
        console.log('Toast component created:', toastRef);

        // Apply configuration immediately
        if (config) {
            const configWithDefaults = {
                ...config,
                theme: (config as any).theme || this.defaultTheme,
                duration: (config as any).duration !== undefined ? (config as any).duration : 4000,
                closable: (config as any).closable !== false
            };
            Object.assign(toastRef.instance, configWithDefaults);
        }

        // Store reference immediately
        this.toasts.set(id, toastRef);

        // Set up promise resolution but don't block the creation
        const promise = new Promise<ToastResult>(resolve => {
            let isResolved = false;
            let timeoutId: number | undefined;

            const resolveOnce = (result: ToastResult) => {
                if (!isResolved) {
                    isResolved = true;
                    if (timeoutId) {
                        clearTimeout(timeoutId);
                    }
                    resolve(result);
                }
            };

            // Listen to close events from the toast component
            const instance = toastRef.instance as any;
            if (instance.closed && typeof instance.closed.subscribe === 'function') {
                instance.closed.subscribe((result: ToastResult) => {
                    console.log('Toast service received event:', result);
                    this.dismiss(id);
                    resolveOnce(result);
                });
            }

            // Auto dismiss if duration is set
            const duration = (config as any)?.duration;
            if (duration !== 0 && duration !== false) {
                timeoutId = window.setTimeout(() => {
                    if (this.toasts.has(id) && !isResolved) {
                        this.dismiss(id);
                        resolveOnce({ dismissed: true });
                    }
                }, duration || 4000);
            }
        });

        // Trigger entrance animation after a short delay
        setTimeout(() => {
            const instance = toastRef.instance as any;
            if (instance && typeof instance.ngOnInit === 'function') {
                instance.ngOnInit();
            }
        }, 10);

        return promise;
    }

    dismiss(id: number) {
        const toastRef = this.toasts.get(id);
        if (toastRef) {
            // Remove from map immediately to prevent double dismissal
            this.toasts.delete(id);

            // Trigger exit animation
            const instance = toastRef.instance as any;
            if (instance.startExitAnimation) {
                instance.startExitAnimation();
            }

            // Remove after animation
            setTimeout(() => {
                try {
                    toastRef.destroy();
                } catch (error) {
                    // Component might already be destroyed
                    console.warn('Toast component already destroyed:', error);
                }
            }, 400);
        }
    }

    success(config?: Partial<ToastConfig>): Promise<ToastResult> {
        return this.show(SuccessToastComponent, {
            title: 'Success',
            message: 'Operation completed successfully!',
            icon: 'circle-check',
            ...config
        });
    }

    error(config?: Partial<ErrorToastConfig>): Promise<ToastResult> {
        return this.show(ErrorToastComponent, {
            title: 'Error',
            message: 'An error occurred. Please try again.',
            icon: 'alert-circle',
            showRetryButton: false,
            ...config
        });
    }

    warning(config?: Partial<WarningToastConfig>): Promise<ToastResult> {
        return this.show(WarningToastComponent, {
            title: 'Warning',
            message: 'Please review the following information carefully.',
            icon: 'alert-triangle',
            showActionButton: false,
            ...config
        });
    }

    info(config?: Partial<InfoToastConfig>): Promise<ToastResult> {
        return this.show(InfoToastComponent, {
            title: 'Information',
            message: 'Here is some important information for you.',
            icon: 'info',
            showLearnMoreButton: false,
            ...config
        });
    }

    loading(config?: Partial<LoadingToastConfig>): Promise<ToastResult> {
        return this.show(LoadingToastComponent, {
            title: 'Loading...',
            message: 'Please wait while we process your request.',
            duration: 0, // Don't auto-dismiss loading toasts
            indeterminate: true,
            showCancelButton: false,
            ...config
        });
    }

    custom(config?: Partial<CustomToastConfig>): Promise<ToastResult> {
        return this.show(CustomToastComponent, {
            title: 'Custom Toast',
            message: 'This is a custom toast message.',
            type: 'default',
            showIcon: true,
            size: 'medium',
            actions: [],
            allowHtml: false,
            ...config
        });
    }

    setPosition(position: ToastPosition) {
        this.defaultPosition = position;
        if (this.containerRef) {
            this.containerRef.instance.setPosition(position);
        }
    }

    setTheme(theme: ToastTheme) {
        this.defaultTheme = theme;
    }

    clear() {
        this.toasts.forEach((_, id) => {
            this.dismiss(id);
        });
    }

    promise<T>(promise: Promise<T>, config?: {
        loading?: string;
        success?: string;
        error?: string;
        theme?: ToastTheme;
        duration?: number;
    }): Promise<ToastResult> {
        // Show loading toast
        const loadingPromise = this.loading({
            title: config?.loading || 'Loading...',
            theme: config?.theme,
            duration: 0
        });

        // Handle promise resolution
        promise
            .then((result) => {
                this.success({
                    title: config?.success || 'Success!',
                    message: (result as any)?.message || 'Operation completed successfully',
                    theme: config?.theme,
                    duration: config?.duration || 3000
                });
            })
            .catch((error) => {
                this.error({
                    title: config?.error || 'Error!',
                    message: error?.message || 'Something went wrong',
                    theme: config?.theme,
                    duration: config?.duration || 4000
                });
            });

        return loadingPromise;
    }
}
