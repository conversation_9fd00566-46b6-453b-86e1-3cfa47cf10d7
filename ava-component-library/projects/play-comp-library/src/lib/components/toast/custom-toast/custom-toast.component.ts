import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
    Output,
    EventEmitter,
    OnInit,
    OnDestroy
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../icon/icon.component';
import { ButtonComponent } from '../../button/button.component';
import { ToastResult, ToastTheme, ToastType, ToastAction } from '../toast-service';

@Component({
    selector: 'ava-custom-toast',
    imports: [CommonModule, IconComponent, ButtonComponent],
    templateUrl: './custom-toast.component.html',
    styleUrl: './custom-toast.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class CustomToastComponent implements OnInit, OnDestroy {
    @Input() title?: string;
    @Input() message?: string;
    @Input() type: ToastType = 'default';
    @Input() icon?: string;
    @Input() iconColor?: string;
    @Input() iconSize = 24;
    @Input() theme: ToastTheme = 'light';
    @Input() duration = 4000;
    @Input() closable = true;
    @Input() showProgress = true;
    @Input() showIcon = true;
    @Input() size: 'small' | 'medium' | 'large' = 'medium';
    @Input() actions: ToastAction[] = [];
    @Input() customContent?: string;
    @Input() allowHtml = false;
    @Input() customIcon?: string;
    @Input() customIconColor?: string;

    @Output() closed = new EventEmitter<ToastResult>();

    isVisible = false;
    isExiting = false;
    progressWidth = 0;
    private progressTimer?: number;

    constructor(private cdr: ChangeDetectorRef) {}

    ngOnInit() {
        // Set default icon and color based on type if not provided
        if (!this.icon && !this.customIcon) {
            this.icon = this.getDefaultIcon();
        }
        
        if (!this.iconColor && !this.customIconColor) {
            this.iconColor = this.getDefaultIconColor();
        }

        // Trigger entrance animation
        setTimeout(() => {
            this.isVisible = true;
            this.cdr.detectChanges();
        }, 10);

        // Start progress bar animation if enabled
        if (this.showProgress && this.duration > 0) {
            this.startProgressAnimation();
        }
    }

    ngOnDestroy() {
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
        }
    }

    private getDefaultIcon(): string {
        const iconMap: Record<ToastType, string> = {
            default: 'info',
            success: 'circle-check',
            error: 'alert-circle',
            warning: 'alert-triangle',
            info: 'info',
            loading: 'loader'
        };
        return iconMap[this.type];
    }

    private getDefaultIconColor(): string {
        const colorMap: Record<ToastType, string> = {
            default: 'var(--toast-title-color)',
            success: 'var(--toast-success-text)',
            error: 'var(--toast-error-text)',
            warning: 'var(--toast-warning-text)',
            info: 'var(--toast-info-text)',
            loading: 'var(--color-text-primary)'
        };
        return colorMap[this.type];
    }

    private startProgressAnimation() {
        if (this.duration <= 0) return;

        const interval = 50; // Update every 50ms
        const steps = this.duration / interval;
        const increment = 100 / steps;
        let currentStep = 0;

        this.progressTimer = window.setInterval(() => {
            currentStep++;
            this.progressWidth = Math.min(currentStep * increment, 100);
            this.cdr.detectChanges();

            if (this.progressWidth >= 100) {
                this.onClose();
            }
        }, interval);
    }

    startExitAnimation() {
        this.isExiting = true;
        this.cdr.detectChanges();
    }

    onClose() {
        console.log('Custom toast close button clicked');
        this.closed.emit({ action: 'close' });
    }

    onActionClick(action: ToastAction) {
        console.log('Custom toast action clicked:', action.action);
        this.closed.emit({ 
            action: action.action || action.label.toLowerCase().replace(/\s+/g, '_'),
            data: action 
        });
    }

    onToastClick() {
        // Optional: Handle toast click
    }

    get displayIcon(): string {
        return this.customIcon || this.icon || this.getDefaultIcon();
    }

    get displayIconColor(): string {
        return this.customIconColor || this.iconColor || this.getDefaultIconColor();
    }

    get displayContent(): string {
        return this.customContent || this.message || '';
    }

    get hasContent(): boolean {
        return !!(this.title || this.displayContent);
    }

    get hasActions(): boolean {
        return this.actions && this.actions.length > 0;
    }
}
