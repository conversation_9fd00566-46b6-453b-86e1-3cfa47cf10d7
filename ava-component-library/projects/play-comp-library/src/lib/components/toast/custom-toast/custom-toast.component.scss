/* Custom Toast Component - Uses existing _toast.css tokens */

.ava-toast.custom {
    display: flex;
    align-items: flex-start;
    gap: var(--global-spacing-3);
    background: var(--toast-background);
    border: 1px solid var(--toast-border);
    border-radius: var(--toast-border-radius);
    box-shadow: var(--toast-shadow);
    padding: var(--toast-padding);
    max-width: var(--toast-max-width);
    min-width: var(--toast-min-width);
    position: relative;
    pointer-events: auto;
    opacity: 0;
    transform: translateX(100%);
    transition: var(--toast-transition);

    /* Animation States */
    &.show {
        opacity: 1;
        transform: translateX(0);
    }

    &.hide {
        opacity: 0;
        transform: translateX(100%);
    }

    /* Theme Variants */
    &.theme-dark {
        background: var(--toast-background-dark);
        
        .toast-title {
            font: var(--toast-title-font-dark);
            color: var(--toast-title-color-dark);
        }
        
        .toast-message {
            font: var(--toast-message-font-dark);
            color: var(--toast-message-color-dark);
        }
    }

    /* Size Variants */
    &.size-small {
        padding: var(--toast-size-sm-padding);
        max-width: var(--toast-size-sm-max-width);
        
        .toast-title,
        .toast-message {
            font: var(--toast-size-sm-font);
        }
    }

    &.size-medium {
        padding: var(--toast-size-md-padding);
        max-width: var(--toast-size-md-max-width);
        
        .toast-title,
        .toast-message {
            font: var(--toast-size-md-font);
        }
    }

    &.size-large {
        padding: var(--toast-size-lg-padding);
        max-width: var(--toast-size-lg-max-width);
        
        .toast-title,
        .toast-message {
            font: var(--toast-size-lg-font);
        }
    }

    /* Type Variants */
    &.type-success {
        background: var(--toast-success-background);
        border-color: var(--toast-success-border);
        
        .toast-title,
        .toast-message {
            color: var(--toast-success-text);
        }
    }

    &.type-error {
        background: var(--toast-error-background);
        border-color: var(--toast-error-border);
        
        .toast-title,
        .toast-message {
            color: var(--toast-error-text);
        }
    }

    &.type-warning {
        background: var(--toast-warning-background);
        border-color: var(--toast-warning-border);
        
        .toast-title,
        .toast-message {
            color: var(--toast-warning-text);
        }
    }

    &.type-info {
        background: var(--toast-info-background);
        border-color: var(--toast-info-border);
        
        .toast-title,
        .toast-message {
            color: var(--toast-info-text);
        }
    }

    /* Icon Section */
    .toast-icon {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Content Section */
    .toast-content {
        flex: 1;
        min-width: 0;
    }

    .toast-title {
        font: var(--toast-title-font);
        color: var(--toast-title-color);
        font-weight: var(--toast-title-weight);
        line-height: var(--toast-title-line-height);
        margin-bottom: var(--toast-title-margin-bottom);
    }

    .toast-message {
        font: var(--toast-message-font);
        color: var(--toast-message-color);
        font-weight: var(--toast-message-weight);
        line-height: var(--toast-message-line-height);
        margin-bottom: var(--toast-message-margin-bottom);
        word-wrap: break-word;
        
        &:last-child {
            margin-bottom: 0;
        }
    }

    /* Actions Section */
    .toast-actions {
        display: flex;
        gap: var(--global-spacing-2);
        flex-wrap: wrap;
        margin-top: var(--global-spacing-2);
    }

    /* Close Button */
    .toast-close {
        position: absolute;
        top: var(--global-spacing-2);
        right: var(--global-spacing-2);
        flex-shrink: 0;
    }

    /* Progress Bar */
    .toast-progress {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: currentColor;
        opacity: 0.3;
        transition: width linear;
        border-radius: 0 0 var(--toast-border-radius) var(--toast-border-radius);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        max-width: 100%;
        margin: 0 var(--global-spacing-2);
        
        .toast-actions {
            flex-direction: column;
            
            ava-button {
                width: 100%;
            }
        }
    }
}
