<div class="ava-toast custom"
     [ngClass]="{
         'show': isVisible,
         'hide': isExiting,
         'theme-light': theme === 'light',
         'theme-dark': theme === 'dark',
         'size-small': size === 'small',
         'size-medium': size === 'medium',
         'size-large': size === 'large',
         'type-success': type === 'success',
         'type-error': type === 'error',
         'type-warning': type === 'warning',
         'type-info': type === 'info',
         'type-loading': type === 'loading'
     }"
     (click)="onToastClick()">
    
    <!-- Icon Section -->
    <div class="toast-icon" *ngIf="showIcon">
        <ava-icon 
            [iconName]="displayIcon"
            [iconSize]="iconSize"
            [iconColor]="displayIconColor"
            aria-hidden="true">
        </ava-icon>
    </div>
    
    <!-- Content Section -->
    <div class="toast-content" *ngIf="hasContent">
        <div class="toast-title" *ngIf="title">{{ title }}</div>
        
        <!-- Message with HTML support -->
        <div class="toast-message" *ngIf="displayContent">
            <span *ngIf="!allowHtml">{{ displayContent }}</span>
            <span *ngIf="allowHtml" [innerHTML]="displayContent"></span>
        </div>
        
        <!-- Custom Actions -->
        <div class="toast-actions" *ngIf="hasActions">
            <ava-button
                *ngFor="let action of actions"
                [label]="action.label"
                [variant]="action.variant || 'secondary'"
                [disabled]="action.disabled || false"
                [iconName]="action.icon || ''"
                [iconPosition]="action.iconPosition || 'left'"
                size="small"
                (userClick)="onActionClick(action)">
            </ava-button>
        </div>
    </div>
    
    <!-- Close Button -->
    <ava-button class="toast-close"
                *ngIf="closable"
                variant="secondary"
                [clear]="true"
                size="small"
                iconPosition="only"
                iconName="x"
                [iconSize]="16"
                (userClick)="onClose()"
                aria-label="Close toast">
    </ava-button>
    
    <!-- Progress Bar -->
    <div class="toast-progress" 
         *ngIf="showProgress && duration > 0"
         [style.width.%]="progressWidth">
    </div>
</div>
